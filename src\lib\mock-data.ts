import type {
  User,
  Admin,
  Teacher,
  Student,
  Parent,
  Class,
  Subject,
  Assignment,
  Grade,
  Attendance,
  Permission,
  RolePermissions,
  Role,
} from "./types"

// Mock Users
export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "admin",
    avatar: "/professional-woman-admin.png",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Admin,
  {
    id: "2",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "teacher",
    avatar: "/male-teacher.png",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Teacher,
  {
    id: "3",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "student",
    avatar: "/diverse-female-student.png",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Student,
  {
    id: "4",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "parent",
    avatar: "/parent-father.png",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Parent,
]

// Mock detailed user data
export const mockAdmins: Admin[] = [
  {
    ...mockUsers[0],
    role: "admin",
    permissions: ["manage_users", "manage_classes", "view_reports", "manage_system"],
  } as Admin,
]

export const mockTeachers: Teacher[] = [
  {
    ...mockUsers[1],
    role: "teacher",
    employeeId: "T001",
    subjects: ["mathematics", "physics"],
    classes: ["10A", "11B"],
    department: "Science",
  } as Teacher,
]

export const mockStudents: Student[] = [
  {
    ...mockUsers[2],
    role: "student",
    studentId: "S001",
    grade: "10",
    class: "10A",
    parentId: "4",
    dateOfBirth: new Date("2008-05-15"),
  } as Student,
]

export const mockParents: Parent[] = [
  {
    ...mockUsers[3],
    role: "parent",
    children: ["3"],
    phone: "******-0123",
  } as Parent,
]

// Mock Classes
export const mockClasses: Class[] = [
  {
    id: "1",
    name: "10A",
    grade: "10",
    teacherId: "2",
    students: ["3"],
    subjects: ["mathematics", "physics", "english"],
  },
]

// Mock Subjects
export const mockSubjects: Subject[] = [
  {
    id: "1",
    name: "Mathematics",
    code: "MATH10",
    teacherId: "2",
    classes: ["10A"],
  },
  {
    id: "2",
    name: "Physics",
    code: "PHYS10",
    teacherId: "2",
    classes: ["10A"],
  },
]

// Mock Assignments
export const mockAssignments: Assignment[] = [
  {
    id: "1",
    title: "Algebra Quiz",
    description: "Basic algebra problems covering linear equations",
    subjectId: "1",
    classId: "1",
    teacherId: "2",
    dueDate: new Date("2024-02-15"),
    createdAt: new Date("2024-02-01"),
  },
]

// Mock Grades
export const mockGrades: Grade[] = [
  {
    id: "1",
    studentId: "3",
    assignmentId: "1",
    score: 85,
    maxScore: 100,
    feedback: "Good work! Focus on quadratic equations.",
    gradedAt: new Date("2024-02-16"),
  },
]

// Mock Attendance
export const mockAttendance: Attendance[] = [
  {
    id: "1",
    studentId: "3",
    classId: "1",
    date: new Date("2024-02-01"),
    status: "present",
  },
]

// Permissions system
export const BASE_PERMISSIONS: Permission[] = [
  // User management
  { id:'users.create', name: 'Create Users', resource: 'users', action: 'create', description: 'Can create new users' },
  { id:'users.view_all', name: 'Read Users', resource: 'users', action: 'read', description: 'Can view user information' },
  { id:'users.update', name: 'Update Users', resource: 'users', action: 'update', description: 'Can update user information' },
  { id:'users.delete', name: 'Delete Users', resource: 'users', action: 'delete', description: 'Can delete users' },
  
  // Role management
  { id: 'roles.create', name: 'Create Roles', resource: 'roles', action: 'create', description: 'Can create new roles' },
  { id: 'roles.read', name: 'Read Roles', resource: 'roles', action: 'read', description: 'Can view role information' },
  { id: 'roles.update', name: 'Update Roles', resource: 'roles', action: 'update', description: 'Can update role information' },
  { id: 'roles.delete', name: 'Delete Roles', resource: 'roles', action: 'delete', description: 'Can delete roles' },
  
  // Permission management
  { id: 'permissions.create', name: 'Create Permissions', resource: 'permissions', action: 'create', description: 'Can create new permissions' },
  { id: 'permissions.read', name: 'Read Permissions', resource: 'permissions', action: 'read', description: 'Can view permissions' },
  { id: 'permissions.update', name: 'Update Permissions', resource: 'permissions', action: 'update', description: 'Can update permissions' },
  { id: 'permissions.delete', name: 'Delete Permissions', resource: 'permissions', action: 'delete', description: 'Can delete permissions' },
  
  // Student management
  { id: 'students.create', name: 'Create Students', resource: 'students', action: 'create', description: 'Can create new students' },
  { id: 'students.read', name: 'Read Students', resource: 'students', action: 'read', description: 'Can view student information' },
  { id: 'students.update', name: 'Update Students', resource: 'students', action: 'update', description: 'Can update student information' },
  { id: 'students.delete', name: 'Delete Students', resource: 'students', action: 'delete', description: 'Can delete students' },
  
  // Teacher management
  { id: 'teachers.create', name: 'Create Teachers', resource: 'teachers', action: 'create', description: 'Can create new teachers' },
  { id: 'teachers.read', name: 'Read Teachers', resource: 'teachers', action: 'read', description: 'Can view teacher information' },
  { id: 'teachers.update', name: 'Update Teachers', resource: 'teachers', action: 'update', description: 'Can update teacher information' },
  { id: 'teachers.delete', name: 'Delete Teachers', resource: 'teachers', action: 'delete', description: 'Can delete teachers' },
  
  // Class management
  { id: 'classes.create', name: 'Create Classes', resource: 'classes', action: 'create', description: 'Can create new classes' },
  { id: 'classes.read', name: 'Read Classes', resource: 'classes', action: 'read', description: 'Can view class information' },
  { id: 'classes.update', name: 'Update Classes', resource: 'classes', action: 'update', description: 'Can update class information' },
  { id: 'classes.delete', name: 'Delete Classes', resource: 'classes', action: 'delete', description: 'Can delete classes' },
  
  // Grade management
  { id: 'grades.create', name: 'Create Grades', resource: 'grades', action: 'create', description: 'Can create grades' },
  { id: 'grades.read', name: 'Read Grades', resource: 'grades', action: 'read', description: 'Can view grades' },
  { id: 'grades.update', name: 'Update Grades', resource: 'grades', action: 'update', description: 'Can update grades' },
  { id: 'grades.delete', name: 'Delete Grades', resource: 'grades', action: 'delete', description: 'Can delete grades' },

  // Communication
  { id: 'communication.view_all', name: 'View All Messages', description: 'Can view all communications', resource: 'Communication', action: 'read' },
  { id: 'communication.send_parents', name: 'Message Parents', description: 'Can send messages to parents', resource: 'Communication', action: 'parents' },
  { id: 'communication.send_teachers', name: 'Message Teachers', description: 'Can send messages to teachers', resource: 'Communication', action: 'teachers' },
  { id: 'communication.send_students', name: 'Message Students', description: 'Can send messages to students', resource: 'Communication', action: 'students' },
  
  // Report management
  { id: 'reports.create', name: 'Create Reports', resource: 'reports', action: 'create', description: 'Can create reports' },
  { id: 'reports.read', name: 'Read Reports', resource: 'reports', action: 'read', description: 'Can view reports' },
  { id: 'reports.update', name: 'Update Reports', resource: 'reports', action: 'update', description: 'Can update reports' },
  { id: 'reports.delete', name: 'Delete Reports', resource: 'reports', action: 'delete', description: 'Can delete reports' },

  // Financial
  { id: 'fees.view_all', name: 'View All Fees', description: 'Can view all fee records', resource: 'Fees', action:'read' },
  { id: 'fees.view_own', name: 'View Own Fees', description: 'Can view own fee records', resource: 'Fees', action:'own' },
  { id: 'fees.create', name: 'Create fees', resource: 'fees', action: 'create', description: 'Can create fees' },
  { id: 'fees.update', name: 'Update fees', resource: 'fees', action: 'update', description: 'Can update fees' },
  { id: 'fees.delete', name: 'Delete fees', resource: 'fees', action: 'delete', description: 'Can delete fees' },
  
  // Analytics
  { id: 'analytics.read', name: 'View Analytics', resource: 'analytics', action: 'read', description: 'Can view system analytics' },
  
  // System settings
  { id: 'settings.update', name: 'Manage Settings', resource: 'settings', action: 'update', description: 'Can manage system settings' },
]

const SYSTEM_ROLES: Role[] = [
  {
    id: 'super_admin',
    name: 'super_admin',
    displayName: 'Super Admin',
    description: 'Super Administrator with all permissions',
    permissions: BASE_PERMISSIONS.map((p) => p.id),
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'school_admin',
    name: 'school_admin',
    displayName: 'School Admin',
    description: 'School Administrator with limited permissions',
    permissions: [
      'users.read', 'users.create', 'users.update',
      'students.read', 'students.create', 'students.update', 'students.delete',
      'teachers.read', 'teachers.create', 'teachers.update', 'teachers.delete',
      'classes.read', 'classes.create', 'classes.update', 'classes.delete',
      'grades.read', 'reports.read', 'reports.create',
      'analytics.read'
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'teacher',
    name: 'teacher',
    displayName: 'Teacher',
    description: 'Teacher with classroom management permissions',
    permissions: [
      'students.read', 'students.update',
      'classes.read', 'classes.update',
      'grades.read', 'grades.create', 'grades.update',
      'reports.read', 'reports.create'
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'student',
    name: 'student',
    displayName: 'Student',
    description: 'Student with limited view permissions',
    permissions: [
      'grades.read', 'classes.read'
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'parent',
    name: 'parent',
    displayName: 'Parent',
    description: 'Parent with view permissions for their children',
    permissions: [
      'students.read', 'grades.read', 'classes.read', 'reports.read'
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

// export const rolePermissions: RolePermissions = {
//   admin: mockPermissions,
//   teacher: mockPermissions.filter((p) => ["grades", "attendance", "classes"].includes(p.resource)),
//   student: mockPermissions.filter((p) => p.action === "view" && ["grades"].includes(p.resource)),
//   parent: mockPermissions.filter((p) => p.action === "view" && ["grades", "attendance"].includes(p.resource)),
// }

// Helper functions
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find((user) => user.id === id)
}

export const getUserByEmail = (email: string): User | undefined => {
  return mockUsers.find((user) => user.email === email)
}

export const getClassesByTeacher = (teacherId: string): Class[] => {
  return mockClasses.filter((cls) => cls.teacherId === teacherId)
}

export const getStudentsByClass = (classId: string): Student[] => {
  const classData = mockClasses.find((cls) => cls.id === classId)
  if (!classData) return []
  return mockStudents.filter((student) => classData.students.includes(student.id))
}
