export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  permissions: string[];
  children?: NavigationItem[];
  badge?: string;
  isExact?: boolean;
}

export const NAVIGATION_CONFIG: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: '📊',
    href: '/dashboard',
    permissions: [], // Everyone can access dashboard
  },
  {
    id: 'students',
    label: 'Students',
    icon: '👥',
    href: '/students',
    permissions: ['students.read', 'students.view_own'],
    children: [
      {
        id: 'students_list',
        label: 'All Students',
        icon: '📋',
        href: '/students',
        permissions: ['students.read'],
        isExact: true,
      },
      {
        id: 'students_add',
        label: 'Add Student',
        icon: '➕',
        href: '/students/add',
        permissions: ['students.create'],
      },
    ],
  },
  {
    id: 'teachers',
    label: 'Teachers',
    icon: '👨‍🏫',
    href: '/teachers',
    permissions: ['teachers.read'],
    children: [
      {
        id: 'teachers_list',
        label: 'All Teachers',
        icon: '📋',
        href: '/teachers',
        permissions: ['teachers.read'],
        isExact: true,
      },
      {
        id: 'teachers_add',
        label: 'Add Teacher',
        icon: '➕',
        href: '/teachers/add',
        permissions: ['teachers.create'],
      },
    ],
  },
  {
    id: 'classes',
    label: 'Classes',
    icon: '🏫',
    href: '/classes',
    permissions: ['classes.read'],
    children: [
      {
        id: 'classes_list',
        label: 'All Classes',
        icon: '📋',
        href: '/classes',
        permissions: ['classes.read'],
        isExact: true,
      },
      {
        id: 'classes_add',
        label: 'Create Class',
        icon: '➕',
        href: '/classes/add',
        permissions: ['classes.create'],
      },
    ],
  },
  {
    id: 'grades',
    label: 'Grades',
    icon: '📝',
    href: '/grades',
    permissions: ['grades.view_all', 'grades.view_own', 'grades.view_children', 'grades.view_class'],
  },
  {
    id: 'attendance',
    label: 'Attendance',
    icon: '✅',
    href: '/attendance',
    permissions: ['attendance.view_all', 'attendance.view_own', 'attendance.view_children', 'attendance.view_class'],
  },
  {
    id: 'assignments',
    label: 'Assignments',
    icon: '📋',
    href: '/assignments',
    permissions: ['assignments.view_all', 'assignments.view_own', 'assignments.view_children'],
  },
  {
    id: 'communications',
    label: 'Messages',
    icon: '💬',
    href: '/communications',
    permissions: ['communication.view_all', 'communication.send_parents', 'communication.send_teachers'],
  },
  {
    id: 'fees',
    label: 'Fees',
    icon: '💳',
    href: '/fees',
    permissions: ['fees.read', 'fees.view_own', 'fees.manage'],
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: '📈',
    href: '/reports',
    permissions: ['reports.view', 'reports.generate'],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: '⚙️',
    href: '/settings',
    permissions: ['settings.manage'],
    children: [
      {
        id: 'settings_school',
        label: 'School Settings',
        icon: '🏫',
        href: '/settings/school',
        permissions: ['settings.manage'],
      },
      {
        id: 'settings_roles',
        label: 'Manage Roles',
        icon: '👤',
        href: '/settings/roles',
        permissions: ['roles.manage'],
      },
      {
        id: 'settings_users',
        label: 'Manage Users',
        icon: '👥',
        href: '/settings/users',
        permissions: ['users.manage'],
      },
    ],
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: '👤',
    href: '/profile',
    permissions: [], // Everyone can access their profile
  },
];

export function getFilteredNavigation(userPermissions: string[]): NavigationItem[] {
  return NAVIGATION_CONFIG.filter(item => {
    // If no permissions required, show to everyone
    if (item.permissions.length === 0) return true;
    
    // Check if user has at least one required permission
    return item.permissions.some(permission => userPermissions.includes(permission));
  }).map(item => ({
    ...item,
    children: item.children?.filter(child => {
      if (child.permissions.length === 0) return true;
      return child.permissions.some(permission => userPermissions.includes(permission));
    }),
  }));
}