import type React from "react";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { ProtectedRoute } from "@/app/(auth)/_components/protected-route";

export default async function AppLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ school: string }>;
}) {
  const requiredRole = ["admin", "teacher"];
  const schoolSlug = await params;

  return (
    <ProtectedRoute requiredRole={requiredRole}>
      <SidebarProvider>
        <AppSidebar schoolSlug={schoolSlug} />

        <SidebarInset>
          <DashboardHeader
            activeSessionId={activeSession.id}
            user={session.user}
            avatar={checkSessionUser.fileUrl}
          />
          <div className="flex-1 overflow-y-auto p-6">{children}</div>
        </SidebarInset>
      </SidebarProvider>
    </ProtectedRoute>
  );
}
